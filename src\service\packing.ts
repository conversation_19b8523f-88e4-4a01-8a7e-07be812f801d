/**
 * 装箱功能相关的API接口
 */

import type {
  WorkOrderListResponse,
  WorkOrderDetailResponse,
  PackingConfirmRequest,
  PackingConfirmResponse,
  ApiResponse,
} from '@/types/packing'

// 模拟延迟函数
const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms))

/**
 * 获取工单列表
 */
export const getWorkOrderList = async (): Promise<ApiResponse<WorkOrderListResponse>> => {
  await delay(500) // 模拟网络延迟

  return {
    code: 200,
    message: '获取成功',
    success: true,
    data: {
      workOrders: [
        { label: 'WO-001-PRJ-001', value: 'WO-001-PRJ-001' },
        { label: 'WO-002-PRJ-002', value: 'WO-002-PRJ-002' },
        { label: 'WO-003-PRJ-003', value: 'WO-003-PRJ-003' },
        { label: 'WO-004-PRJ-004', value: 'WO-004-PRJ-004' },
        { label: 'WO-005-PRJ-005', value: 'WO-005-PRJ-005' },
      ],
      total: 5,
    },
  }
}

/**
 * 获取工单详情
 * @param workOrderNumber 工单编号
 */
export const getWorkOrderDetail = async (
  workOrderNumber: string,
): Promise<ApiResponse<WorkOrderDetailResponse>> => {
  console.log('API: 接收到的工单编号:', workOrderNumber) // 调试信息
  await delay(300) // 模拟网络延迟

  // 模拟工单数据
  const mockWorkOrderData = {
    'WO-001-PRJ-001': {
      projectCode: 'PRJ-2025-001',
      batchCode: 'BATCH-001',
      workOrderNumber: 'WO-001',
      workOrderType: '标准包装',
      deliveryAddress: '例：上海市浦东新区xx科技园区',
    },
    'WO-002-PRJ-002': {
      projectCode: 'PRJ-2025-002',
      batchCode: 'BATCH-002',
      workOrderNumber: 'WO-002',
      workOrderType: '特殊包装',
      deliveryAddress: '例：北京市朝阳区xx工业园区',
    },
    'WO-003-PRJ-003': {
      projectCode: 'PRJ-2025-003',
      batchCode: 'BATCH-003',
      workOrderNumber: 'WO-003',
      workOrderType: '标准包装',
      deliveryAddress: '例：深圳市南山区xx科技园区',
    },
    'WO-004-PRJ-004': {
      projectCode: 'PRJ-2025-004',
      batchCode: 'BATCH-004',
      workOrderNumber: 'WO-004',
      workOrderType: '快速包装',
      deliveryAddress: '例：广州市天河区xx产业园区',
    },
    'WO-005-PRJ-005': {
      projectCode: 'PRJ-2025-005',
      batchCode: 'BATCH-005',
      workOrderNumber: 'WO-005',
      workOrderType: '标准包装',
      deliveryAddress: '例：杭州市西湖区xx科技园区',
    },
  }

  const workOrderInfo = mockWorkOrderData[workOrderNumber as keyof typeof mockWorkOrderData]
  console.log('API: 查找到的工单信息:', workOrderInfo) // 调试信息
  console.log('API: 可用的工单键:', Object.keys(mockWorkOrderData)) // 调试信息

  if (!workOrderInfo) {
    return {
      code: 404,
      message: '工单不存在',
      success: false,
      data: null,
    }
  }

  return {
    code: 200,
    message: '获取成功',
    success: true,
    data: {
      workOrderInfo,
    },
  }
}

/**
 * 确认装箱
 * @param request 装箱确认请求
 */
export const confirmPacking = async (
  request: PackingConfirmRequest,
): Promise<ApiResponse<PackingConfirmResponse>> => {
  await delay(1000) // 模拟网络延迟和后端处理时间

  // 生成KD码（模拟后端生成逻辑）
  const timestamp = Date.now().toString().slice(-8)
  const kdCode = `KD${timestamp}BIY`

  // 生成二维码数据（实际应该是包含KD码的JSON或特定格式）
  const qrCodeData = JSON.stringify({
    kdCode,
    projectCode: request.projectCode,
    batchCode: request.batchCode,
    timestamp: new Date().toISOString(),
  })

  // 生成产品列表（模拟后端处理产品码）
  const productList = request.productCodes.map((code, index) => ({
    productCode: `CP${String(index + 1).padStart(4, '0')}`,
    productName: `产品${index + 1}`,
    specification: `规格${index + 1}`,
  }))

  return {
    code: 200,
    message: '装箱成功',
    success: true,
    data: {
      kdCode,
      qrCodeData,
      productList,
      packingTime: new Date().toISOString(),
      operator: '操作员001',
    },
  }
}

/**
 * 模拟扫码功能
 */
export const simulateScanCode = async (): Promise<string> => {
  await delay(800) // 模拟扫码时间

  // 模拟扫码结果
  const mockCodes = [
    'KD1309600BIY',
    'KD1309601BIY',
    'KD1309602BIY',
    'KD1309603BIY',
    'KD1309604BIY',
    'KD1309605BIY',
  ]

  const randomIndex = Math.floor(Math.random() * mockCodes.length)
  return mockCodes[randomIndex]
}

/**
 * 验证产品码
 * @param productCode 产品码
 */
export const validateProductCode = async (productCode: string): Promise<ApiResponse<boolean>> => {
  await delay(200) // 模拟验证时间

  // 简单的产品码格式验证（实际应该调用后端验证）
  const isValid = productCode.length >= 6

  return {
    code: 200,
    message: isValid ? '产品码有效' : '产品码格式无效',
    success: true,
    data: isValid,
  }
}
