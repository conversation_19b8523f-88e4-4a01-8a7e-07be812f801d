<!-- 装箱作业页面 -->
<route lang="json5">
{
  style: {
    navigationBarTitleText: '装箱作业',
    navigationBarBackgroundColor: '#ffffff',
    navigationBarTextStyle: 'black',
  },
}
</route>
<template>
  <view class="packing-page bg-gray-50 min-h-screen">
    <!-- 工单选择区域 -->
    <view class="work-order-section bg-white p-4 mb-2">
      <view class="section-title text-base font-medium text-gray-800 mb-3">选择包装工单</view>
      <wd-select-picker
        v-model="selectedWorkOrder"
        :columns="workOrderOptions"
        type="radio"
        title="选择工单"
        placeholder="请选择工单"
        :loading="loading"
        @confirm="onWorkOrderChange"
      />
    </view>

    <!-- 工单信息展示区域 -->
    <view v-if="workOrderInfo" class="work-order-info bg-white p-4 mb-2">
      <wd-row :gutter="12" class="mb-3">
        <wd-col :span="12">
          <view class="info-item">
            <view class="label text-sm text-gray-600 mb-1">项目代码</view>
            <wd-input
              v-model="workOrderInfo.projectCode"
              placeholder="项目代码"
              readonly
              class="info-input"
            />
          </view>
        </wd-col>
        <wd-col :span="12">
          <view class="info-item">
            <view class="label text-sm text-gray-600 mb-1">批次码</view>
            <wd-input
              v-model="workOrderInfo.batchCode"
              placeholder="批次码"
              readonly
              class="info-input"
            />
          </view>
        </wd-col>
      </wd-row>

      <wd-row :gutter="12" class="mb-3">
        <wd-col :span="12">
          <view class="info-item">
            <view class="label text-sm text-gray-600 mb-1">工单编号</view>
            <wd-input
              v-model="workOrderInfo.workOrderNumber"
              placeholder="工单编号"
              readonly
              class="info-input"
            />
          </view>
        </wd-col>
        <wd-col :span="12">
          <view class="info-item">
            <view class="label text-sm text-gray-600 mb-1">工单类型</view>
            <wd-input
              v-model="workOrderInfo.workOrderType"
              placeholder="工单类型"
              readonly
              class="info-input"
            />
          </view>
        </wd-col>
      </wd-row>

      <view class="info-item">
        <view class="label text-sm text-gray-600 mb-1">收货地址</view>
        <wd-input
          v-model="workOrderInfo.deliveryAddress"
          placeholder="收货地址"
          readonly
          class="info-input"
        />
      </view>
    </view>

    <!-- 产品码录入区域 -->
    <view class="product-input-section bg-white p-4 mb-2">
      <view class="section-title text-base font-medium text-gray-800 mb-3">产品码录入</view>
      <view class="input-container flex items-center gap-2">
        <wd-input
          v-model="productCode"
          placeholder="请扫描或输入产品码"
          class="flex-1"
          @confirm="addProductCode"
        />
        <wd-button type="primary" size="small" :loading="loading" @click="scanCode">
          <wd-icon name="scan" size="16px" class="mr-1"></wd-icon>
          扫码
        </wd-button>
      </view>
    </view>

    <!-- 产品列表 -->
    <view v-if="productList.length > 0" class="product-list bg-white p-4 mb-2">
      <view class="section-title text-base font-medium text-gray-800 mb-3">
        已录入产品 ({{ productList.length }})
      </view>
      <view class="product-item" v-for="(item, index) in productList" :key="index">
        <view
          class="flex items-center justify-between py-2 border-b border-gray-100 last:border-b-0"
        >
          <view class="product-code text-sm text-gray-800">{{ item }}</view>
          <wd-button type="error" size="small" plain @click="removeProduct(index)">删除</wd-button>
        </view>
      </view>
    </view>

    <!-- 确认按钮 -->
    <view class="confirm-section p-4">
      <wd-button
        type="primary"
        size="large"
        block
        :disabled="!canConfirm"
        :loading="loading"
        @click="confirmPacking"
      >
        确认
      </wd-button>
    </view>

    <!-- 确认产品信息弹框 -->
    <ConfirmProductDialog
      v-model="showConfirmDialog"
      :kd-code="kdCode"
      :product-list="confirmProductList"
      :loading="loading"
      @confirm="submitPacking"
      @cancel="cancelConfirm"
    />

    <!-- 装箱打印弹框 -->
    <PackingPrintDialog
      v-model="showPrintDialog"
      :print-info="printInfo"
      :product-count="productList.length"
      :printing="printing"
      @print="printLabel"
      @close="closePrintDialog"
    />
  </view>
</template>

<script lang="ts" setup>
import { useToast } from 'wot-design-uni'
import type { WorkOrderInfo, WorkOrderOption, ProductInfo, PrintInfo } from '@/types/packing'
import {
  getWorkOrderList,
  getWorkOrderDetail,
  confirmPacking as submitPackingRequest,
  simulateScanCode,
  validateProductCode,
} from '@/service/packing'
import ConfirmProductDialog from './components/ConfirmProductDialog.vue'
import PackingPrintDialog from './components/PackingPrintDialog.vue'

defineOptions({
  name: 'PackingPage',
})

const toast = useToast()

// 响应式数据
const selectedWorkOrder = ref<string>('')
const workOrderInfo = ref<WorkOrderInfo | null>(null)
const productCode = ref<string>('')
const productList = ref<string[]>([])
const showConfirmDialog = ref<boolean>(false)
const showPrintDialog = ref<boolean>(false)
const kdCode = ref<string>('')
const confirmProductList = ref<ProductInfo[]>([])
const printInfo = ref<PrintInfo>({} as PrintInfo)
const loading = ref<boolean>(false)
const printing = ref<boolean>(false)

// 工单选项
const workOrderOptions = ref<WorkOrderOption[]>([])

// 计算属性：是否可以确认
const canConfirm = computed(() => {
  return selectedWorkOrder.value && productList.value.length > 0
})

// 初始化数据
onMounted(async () => {
  await loadWorkOrderList()
})

// 加载工单列表
const loadWorkOrderList = async () => {
  try {
    const response = await getWorkOrderList()
    if (response.success) {
      workOrderOptions.value = response.data.workOrders
    } else {
      toast.show(response.message)
    }
  } catch (error) {
    console.error('加载工单列表失败:', error)
    toast.show('加载工单列表失败')
  }
}

// 工单变更处理
const onWorkOrderChange = async (value: any) => {
  console.log('选择的工单:', value) // 调试信息

  // 从 wd-select-picker 返回的对象中提取实际的值
  const workOrderValue = typeof value === 'string' ? value : value.value
  console.log('提取的工单值:', workOrderValue) // 调试信息

  try {
    loading.value = true
    const response = await getWorkOrderDetail(workOrderValue)
    console.log('工单详情响应:', response) // 调试信息
    if (response.success) {
      workOrderInfo.value = response.data.workOrderInfo
      toast.show('工单信息加载成功')
    } else {
      toast.show(response.message)
      workOrderInfo.value = null
    }
  } catch (error) {
    console.error('获取工单详情失败:', error)
    toast.show('获取工单详情失败')
    workOrderInfo.value = null
  } finally {
    loading.value = false
  }
}

// 扫码功能
const scanCode = async () => {
  try {
    loading.value = true
    toast.show('正在扫码...')
    const scannedCode = await simulateScanCode()
    productCode.value = scannedCode
    await addProductCode()
  } catch (error) {
    console.error('扫码失败:', error)
    toast.show('扫码失败')
  } finally {
    loading.value = false
  }
}

// 添加产品码
const addProductCode = async () => {
  if (!productCode.value.trim()) {
    toast.show('请输入产品码')
    return
  }

  if (productList.value.includes(productCode.value)) {
    toast.show('产品码已存在')
    return
  }

  try {
    // 验证产品码格式
    const validateResponse = await validateProductCode(productCode.value)
    if (!validateResponse.data) {
      toast.show(validateResponse.message)
      return
    }

    productList.value.push(productCode.value)
    productCode.value = ''
    toast.show('产品码添加成功')
  } catch (error) {
    console.error('验证产品码失败:', error)
    toast.show('验证产品码失败')
  }
}

// 删除产品
const removeProduct = (index: number) => {
  productList.value.splice(index, 1)
  toast.show('产品已删除')
}

// 确认装箱
const confirmPacking = () => {
  if (!workOrderInfo.value) {
    toast.show('请先选择工单')
    return
  }

  // 生成临时KD码用于确认弹框显示
  const timestamp = Date.now().toString().slice(-8)
  kdCode.value = `KD${timestamp}BIY`

  // 生成确认产品列表
  confirmProductList.value = productList.value.map((_, index) => ({
    productCode: `CP${String(index + 1).padStart(4, '0')}`,
    productName: `产品${index + 1}`,
    specification: `规格${index + 1}`,
  }))

  showConfirmDialog.value = true
}

// 取消确认
const cancelConfirm = () => {
  showConfirmDialog.value = false
}

// 提交装箱
const submitPacking = async () => {
  if (!workOrderInfo.value) {
    toast.show('工单信息不存在')
    return
  }

  try {
    loading.value = true
    showConfirmDialog.value = false

    const request = {
      workOrderNumber: workOrderInfo.value.workOrderNumber,
      productCodes: productList.value,
      projectCode: workOrderInfo.value.projectCode,
      batchCode: workOrderInfo.value.batchCode,
    }

    const response = await submitPackingRequest(request)

    if (response.success) {
      // 更新KD码为后端返回的真实KD码
      kdCode.value = response.data.kdCode
      confirmProductList.value = response.data.productList

      // 设置打印信息
      printInfo.value = {
        projectCode: workOrderInfo.value.projectCode,
        batchCode: workOrderInfo.value.batchCode,
        kdCode: response.data.kdCode,
        qrCodeData: response.data.qrCodeData,
        packingTime: response.data.packingTime,
      }

      showPrintDialog.value = true
      toast.show('装箱成功')
    } else {
      toast.show(response.message)
    }
  } catch (error) {
    console.error('装箱失败:', error)
    toast.show('装箱失败')
  } finally {
    loading.value = false
  }
}

// 关闭打印弹框
const closePrintDialog = () => {
  showPrintDialog.value = false
  // 重置数据
  productList.value = []
  selectedWorkOrder.value = ''
  workOrderInfo.value = null
  kdCode.value = ''
  confirmProductList.value = []
  printInfo.value = {} as PrintInfo
}

// 打印标签
const printLabel = () => {
  // 这里可以调用打印API或者调用设备打印功能
  printing.value = true
  toast.show('正在打印...')

  // 模拟打印过程
  setTimeout(() => {
    printing.value = false
    toast.show('打印完成')
    closePrintDialog()
  }, 2000)
}
</script>

<style scoped>
.packing-page {
  padding-bottom: 100px;
}

.section-title {
  border-left: 4px solid #3b82f6;
  padding-left: 8px;
}

.info-input {
  background-color: #f9fafb;
}

.product-item:last-child {
  border-bottom: none;
}
</style>
