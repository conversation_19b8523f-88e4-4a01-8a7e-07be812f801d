import { IUserInfo } from '@/service/login/types'
import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useUserStore = defineStore(
  'user',
  () => {
    const userInfo = ref<IUserInfo>()

    const setUserInfo = (val: IUserInfo) => {
      userInfo.value = val
    }

    const clearUserInfo = () => {
      userInfo.value = undefined
    }
    // 一般没有reset需求，不需要的可以删除
    const reset = () => {
      userInfo.value = undefined
    }
    const isLogined = computed(() => !!userInfo.value?.token)

    return {
      userInfo,
      setUserInfo,
      clearUserInfo,
      isLogined,
      reset,
    }
  },
  {
    persist: true,
  },
)
