<!-- 确认产品信息弹框组件 -->
<template>
  <wd-popup 
    v-model="visible" 
    position="center" 
    :close-on-click-modal="false"
    :custom-style="popupStyle"
  >
    <view class="confirm-dialog" :class="{ 'confirm-dialog--pad': isPad }">
      <!-- 弹框标题 -->
      <view class="dialog-header">
        <view class="dialog-title">确认产品信息</view>
        <view class="dialog-subtitle">请检查产品信息是否正确</view>
      </view>

      <!-- KD码显示区域 -->
      <view class="kd-code-section">
        <view class="kd-code-label">生成的KD码</view>
        <view class="kd-code-value">{{ kdCode }}</view>
      </view>

      <!-- 产品列表表格 -->
      <view class="product-table">
        <view class="table-header">
          <wd-row>
            <wd-col :span="6">
              <view class="table-cell table-cell--header">序号</view>
            </wd-col>
            <wd-col :span="12">
              <view class="table-cell table-cell--header">产品编号</view>
            </wd-col>
            <wd-col :span="6">
              <view class="table-cell table-cell--header">规格</view>
            </wd-col>
          </wd-row>
        </view>
        
        <view class="table-body">
          <view 
            v-for="(product, index) in productList" 
            :key="index" 
            class="table-row"
          >
            <wd-row>
              <wd-col :span="6">
                <view class="table-cell">{{ index + 1 }}</view>
              </wd-col>
              <wd-col :span="12">
                <view class="table-cell">{{ product.productCode }}</view>
              </wd-col>
              <wd-col :span="6">
                <view class="table-cell">{{ product.specification || '-' }}</view>
              </wd-col>
            </wd-row>
          </view>
        </view>
      </view>

      <!-- 统计信息 -->
      <view class="summary-info">
        <view class="summary-item">
          <text class="summary-label">产品总数：</text>
          <text class="summary-value">{{ productList.length }} 件</text>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="dialog-actions">
        <wd-button 
          size="large" 
          :custom-style="cancelButtonStyle"
          @click="handleCancel"
        >
          取消
        </wd-button>
        <wd-button 
          type="primary" 
          size="large" 
          :loading="loading"
          :custom-style="confirmButtonStyle"
          @click="handleConfirm"
        >
          确认装箱
        </wd-button>
      </view>
    </view>
  </wd-popup>
</template>

<script lang="ts" setup>
import type { ProductInfo } from '@/types/packing'

interface Props {
  modelValue: boolean
  kdCode: string
  productList: ProductInfo[]
  loading?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'confirm'): void
  (e: 'cancel'): void
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

const emit = defineEmits<Emits>()

// 计算属性：弹框显示状态
const visible = computed({
  get: () => props.modelValue,
  set: (value: boolean) => emit('update:modelValue', value)
})

// 检测是否为 pad 端
const isPad = computed(() => {
  const { screenWidth } = uni.getSystemInfoSync()
  return screenWidth >= 768
})

// 弹框样式
const popupStyle = computed(() => {
  if (isPad.value) {
    return {
      width: '600px',
      maxHeight: '80vh',
      borderRadius: '16px'
    }
  }
  return {
    width: '90vw',
    maxWidth: '400px',
    maxHeight: '80vh',
    borderRadius: '12px'
  }
})

// 按钮样式
const cancelButtonStyle = computed(() => {
  return {
    flex: '1',
    marginRight: isPad.value ? '16px' : '12px'
  }
})

const confirmButtonStyle = computed(() => {
  return {
    flex: '1'
  }
})

// 事件处理
const handleCancel = () => {
  emit('cancel')
}

const handleConfirm = () => {
  emit('confirm')
}
</script>

<style scoped>
.confirm-dialog {
  background: #ffffff;
  border-radius: 12px;
  overflow: hidden;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}

.confirm-dialog--pad {
  border-radius: 16px;
}

.dialog-header {
  padding: 24px 24px 16px;
  text-align: center;
  border-bottom: 1px solid #f0f0f0;
}

.dialog-title {
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 4px;
}

.dialog-subtitle {
  font-size: 14px;
  color: #666666;
}

.kd-code-section {
  padding: 20px 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  margin: 0;
}

.kd-code-label {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 4px;
}

.kd-code-value {
  font-size: 24px;
  font-weight: bold;
  color: #ffffff;
  font-family: 'Courier New', monospace;
  letter-spacing: 2px;
}

.product-table {
  flex: 1;
  overflow: hidden;
  margin: 16px 24px;
}

.table-header {
  background: #f8f9fa;
  border-radius: 8px 8px 0 0;
  border: 1px solid #e9ecef;
}

.table-body {
  border: 1px solid #e9ecef;
  border-top: none;
  border-radius: 0 0 8px 8px;
  max-height: 200px;
  overflow-y: auto;
}

.table-row {
  border-bottom: 1px solid #f0f0f0;
}

.table-row:last-child {
  border-bottom: none;
}

.table-row:hover {
  background: #f8f9fa;
}

.table-cell {
  padding: 12px 8px;
  text-align: center;
  font-size: 14px;
  color: #333333;
}

.table-cell--header {
  font-weight: 600;
  color: #1a1a1a;
  background: #f8f9fa;
}

.summary-info {
  padding: 16px 24px;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.summary-label {
  font-size: 14px;
  color: #666666;
}

.summary-value {
  font-size: 16px;
  font-weight: 600;
  color: #1890ff;
}

.dialog-actions {
  padding: 20px 24px;
  display: flex;
  gap: 12px;
  border-top: 1px solid #f0f0f0;
}

/* pad 端样式调整 */
.confirm-dialog--pad .dialog-header {
  padding: 32px 32px 20px;
}

.confirm-dialog--pad .dialog-title {
  font-size: 20px;
}

.confirm-dialog--pad .kd-code-section {
  padding: 24px 32px;
}

.confirm-dialog--pad .kd-code-value {
  font-size: 28px;
}

.confirm-dialog--pad .product-table {
  margin: 20px 32px;
}

.confirm-dialog--pad .table-body {
  max-height: 300px;
}

.confirm-dialog--pad .table-cell {
  padding: 16px 12px;
  font-size: 15px;
}

.confirm-dialog--pad .summary-info {
  padding: 20px 32px;
}

.confirm-dialog--pad .dialog-actions {
  padding: 24px 32px;
  gap: 16px;
}

/* 滚动条样式 */
.table-body::-webkit-scrollbar {
  width: 6px;
}

.table-body::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.table-body::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.table-body::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
