// 登录传参
export interface ILogin {
  username: string
  password: string
}

// 登录返回值
export interface ILoginRes {
  token: string
  user: IUserInfo
}

// 用户信息
export interface IUserInfo {
  token: string
  dataScopes: string[]
  roles: string[]
  user: IUserDetail
}
// 用户详情
export interface IUserDetail {
  createBy: string
  createTime: string
  dept: IDept
  email: string
  enabled: boolean
  gender: string
  id: number
  nickName: string
  phone: string
  roles: IRoles
  updateTime: string
  username: string
}
// 部门
export interface IDept {
  id: number
  name: string
  invalidTime: string
}
// 角色
export interface IRoles {
  id: number
  level: number
  name: string
  dataScope: string
}
