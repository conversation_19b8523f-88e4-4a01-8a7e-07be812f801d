<!-- 装箱打印弹框组件 -->
<template>
  <wd-popup 
    v-model="visible" 
    position="center" 
    :close-on-click-modal="false"
    :custom-style="popupStyle"
  >
    <view class="print-dialog" :class="{ 'print-dialog--pad': isPad }">
      <!-- 弹框标题 -->
      <view class="dialog-header">
        <view class="dialog-title">
          <wd-icon name="check-circle" size="24px" color="#52c41a" class="success-icon"></wd-icon>
          装箱成功
        </view>
        <view class="dialog-subtitle">请打印装箱标签</view>
      </view>

      <!-- 打印内容区域 -->
      <view class="print-content">
        <!-- 二维码区域 -->
        <view class="qr-section">
          <view class="qr-code-container">
            <view class="qr-code-placeholder">
              <wd-icon name="qrcode" size="48px" color="#666666"></wd-icon>
              <view class="qr-placeholder-text">二维码</view>
            </view>
          </view>
          <view class="qr-description">扫描二维码查看详情</view>
        </view>

        <!-- 装箱信息 -->
        <view class="packing-info">
          <view class="info-title">装箱信息</view>
          
          <view class="info-grid">
            <view class="info-item">
              <view class="info-label">项目代码</view>
              <view class="info-value">{{ printInfo.projectCode }}</view>
            </view>
            
            <view class="info-item">
              <view class="info-label">批次码</view>
              <view class="info-value">{{ printInfo.batchCode }}</view>
            </view>
            
            <view class="info-item info-item--full">
              <view class="info-label">KD码</view>
              <view class="info-value info-value--kd">{{ printInfo.kdCode }}</view>
            </view>
            
            <view class="info-item" v-if="printInfo.packingTime">
              <view class="info-label">装箱时间</view>
              <view class="info-value">{{ formatTime(printInfo.packingTime) }}</view>
            </view>
            
            <view class="info-item" v-if="productCount">
              <view class="info-label">产品数量</view>
              <view class="info-value">{{ productCount }} 件</view>
            </view>
          </view>
        </view>

        <!-- 打印预览提示 -->
        <view class="print-preview">
          <wd-icon name="info-circle" size="16px" color="#1890ff" class="preview-icon"></wd-icon>
          <text class="preview-text">标签将按照上述信息进行打印</text>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="dialog-actions">
        <wd-button 
          size="large" 
          :custom-style="closeButtonStyle"
          @click="handleClose"
        >
          关闭
        </wd-button>
        <wd-button 
          type="primary" 
          size="large" 
          :loading="printing"
          :custom-style="printButtonStyle"
          @click="handlePrint"
        >
          <wd-icon name="printer" size="16px" class="button-icon"></wd-icon>
          {{ printing ? '打印中...' : '打印标签' }}
        </wd-button>
      </view>
    </view>
  </wd-popup>
</template>

<script lang="ts" setup>
import type { PrintInfo } from '@/types/packing'
import dayjs from 'dayjs'

interface Props {
  modelValue: boolean
  printInfo: PrintInfo
  productCount?: number
  printing?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'print'): void
  (e: 'close'): void
}

const props = withDefaults(defineProps<Props>(), {
  productCount: 0,
  printing: false
})

const emit = defineEmits<Emits>()

// 计算属性：弹框显示状态
const visible = computed({
  get: () => props.modelValue,
  set: (value: boolean) => emit('update:modelValue', value)
})

// 检测是否为 pad 端
const isPad = computed(() => {
  const { screenWidth } = uni.getSystemInfoSync()
  return screenWidth >= 768
})

// 弹框样式
const popupStyle = computed(() => {
  if (isPad.value) {
    return {
      width: '560px',
      maxHeight: '85vh',
      borderRadius: '16px'
    }
  }
  return {
    width: '90vw',
    maxWidth: '380px',
    maxHeight: '85vh',
    borderRadius: '12px'
  }
})

// 按钮样式
const closeButtonStyle = computed(() => {
  return {
    flex: '1',
    marginRight: isPad.value ? '16px' : '12px'
  }
})

const printButtonStyle = computed(() => {
  return {
    flex: '1.5'
  }
})

// 格式化时间
const formatTime = (timeStr: string) => {
  if (!timeStr) return '-'
  return dayjs(timeStr).format('YYYY-MM-DD HH:mm:ss')
}

// 事件处理
const handleClose = () => {
  emit('close')
}

const handlePrint = () => {
  emit('print')
}
</script>

<style scoped>
.print-dialog {
  background: #ffffff;
  border-radius: 12px;
  overflow: hidden;
  max-height: 85vh;
  display: flex;
  flex-direction: column;
}

.print-dialog--pad {
  border-radius: 16px;
}

.dialog-header {
  padding: 24px 24px 16px;
  text-align: center;
  border-bottom: 1px solid #f0f0f0;
}

.dialog-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 4px;
}

.success-icon {
  flex-shrink: 0;
}

.dialog-subtitle {
  font-size: 14px;
  color: #666666;
}

.print-content {
  flex: 1;
  padding: 20px 24px;
  overflow-y: auto;
}

.qr-section {
  text-align: center;
  margin-bottom: 24px;
}

.qr-code-container {
  display: flex;
  justify-content: center;
  margin-bottom: 12px;
}

.qr-code-placeholder {
  width: 120px;
  height: 120px;
  background: #f8f9fa;
  border: 2px dashed #d9d9d9;
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.qr-placeholder-text {
  font-size: 12px;
  color: #999999;
}

.qr-description {
  font-size: 12px;
  color: #666666;
}

.packing-info {
  background: #fafafa;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
}

.info-title {
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e9ecef;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.info-item--full {
  grid-column: 1 / -1;
}

.info-label {
  font-size: 12px;
  color: #666666;
  font-weight: 500;
}

.info-value {
  font-size: 14px;
  color: #1a1a1a;
  font-weight: 500;
}

.info-value--kd {
  font-family: 'Courier New', monospace;
  font-size: 16px;
  color: #1890ff;
  font-weight: 600;
  letter-spacing: 1px;
}

.print-preview {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: #e6f7ff;
  border: 1px solid #91d5ff;
  border-radius: 8px;
}

.preview-icon {
  flex-shrink: 0;
}

.preview-text {
  font-size: 13px;
  color: #1890ff;
}

.dialog-actions {
  padding: 20px 24px;
  display: flex;
  gap: 12px;
  border-top: 1px solid #f0f0f0;
}

.button-icon {
  margin-right: 4px;
}

/* pad 端样式调整 */
.print-dialog--pad .dialog-header {
  padding: 32px 32px 20px;
}

.print-dialog--pad .dialog-title {
  font-size: 20px;
}

.print-dialog--pad .print-content {
  padding: 24px 32px;
}

.print-dialog--pad .qr-code-placeholder {
  width: 140px;
  height: 140px;
}

.print-dialog--pad .packing-info {
  padding: 20px;
}

.print-dialog--pad .info-title {
  font-size: 17px;
  margin-bottom: 20px;
}

.print-dialog--pad .info-grid {
  gap: 20px;
}

.print-dialog--pad .info-label {
  font-size: 13px;
}

.print-dialog--pad .info-value {
  font-size: 15px;
}

.print-dialog--pad .info-value--kd {
  font-size: 18px;
}

.print-dialog--pad .dialog-actions {
  padding: 24px 32px;
  gap: 16px;
}

/* 滚动条样式 */
.print-content::-webkit-scrollbar {
  width: 6px;
}

.print-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.print-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.print-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
