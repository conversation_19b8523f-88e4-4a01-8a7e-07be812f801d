{"compilerOptions": {"composite": true, "skipLibCheck": true, "module": "ESNext", "moduleResolution": "Node", "resolveJsonModule": true, "noImplicitThis": true, "allowSyntheticDefaultImports": true, "allowJs": true, "sourceMap": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"]}, "outDir": "dist", "lib": ["esnext", "dom"], "types": ["@dcloudio/types", "@uni-helper/uni-types", "@types/wechat-miniprogram", "wot-design-uni/global.d.ts", "z-paging/types", "./src/typings.d.ts"]}, "vueCompilerOptions": {"plugins": ["@uni-helper/uni-types/volar-plugin"]}, "exclude": ["node_modules"], "include": ["src/**/*.ts", "src/**/*.js", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.jsx", "src/**/*.vue", "src/**/*.json"]}