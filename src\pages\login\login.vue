<route lang="json5" type="home">
{
  layout: 'default',
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '登录',
  },
}
</route>

<template>
  <view
    class="bg-white overflow-hidden pt-2 px-4"
    :style="{ marginTop: safeAreaInsets?.top + 'px' }"
  >
    <view class="font-bold text-xl mt-10">账号密码登录</view>
    <view class="mt-20 flex flex-col gap-5">
      <wd-input v-model="username" placeholder="请输入用户名" clearable />
      <wd-input v-model="password" placeholder="请输入密码" clearable show-password />

      <wd-button type="primary" block @click="handleLogin" :loading="loading" size="large">
        登录
      </wd-button>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { loginApi } from '@/service/login'
import { useUserStore } from '@/store'
import { useToast } from 'wot-design-uni'
import { encrypt, decrypt } from '@/utils/rsaEncrypt'
const toast = useToast()
const { safeAreaInsets } = uni.getSystemInfoSync()
const userStore = useUserStore()
const loading = ref(false)

// 表单数据
const username = ref('')
const password = ref('')

// 登录处理
const handleLogin = async () => {
  try {
    console.log('resp')

    loading.value = true
    const resp = await loginApi({
      username: username.value,
      password: encrypt(password.value),
    })
    userStore.setUserInfo({
      ...resp.data.user,
      token: resp.data.token,
    })
    // 登录成功处理
    toast.success({
      msg: '登录成功',
    })

    // 登录成功后跳转
    setTimeout(() => {
      uni.switchTab({
        url: '/pages/index/index',
      })
    }, 1500)
  } catch (error) {
    console.log('error', error)
    // 登录失败处理
    toast.error({
      msg: '登录失败，请检查用户名和密码',
    })
  } finally {
    loading.value = false
  }
}

onShow(() => {
  const token = userStore.userInfo?.token
  if (token) {
    uni.switchTab({
      url: '/pages/index/index',
    })
  }
})
</script>

<style lang="scss" scoped></style>
