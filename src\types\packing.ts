/**
 * 装箱功能相关的类型定义
 */

// 工单信息接口
export interface WorkOrderInfo {
  /** 项目代码 */
  projectCode: string
  /** 批次码 */
  batchCode: string
  /** 工单编号 */
  workOrderNumber: string
  /** 工单类型 */
  workOrderType: string
  /** 收货地址 */
  deliveryAddress: string
}

// 工单选项接口
export interface WorkOrderOption {
  /** 显示标签 */
  label: string
  /** 选项值 */
  value: string
}

// 产品信息接口
export interface ProductInfo {
  /** 产品编号 */
  productCode: string
  /** 产品名称 */
  productName?: string
  /** 规格型号 */
  specification?: string
}

// 装箱确认请求接口
export interface PackingConfirmRequest {
  /** 工单编号 */
  workOrderNumber: string
  /** 产品码列表 */
  productCodes: string[]
  /** 项目代码 */
  projectCode: string
  /** 批次码 */
  batchCode: string
}

// 装箱确认响应接口
export interface PackingConfirmResponse {
  /** KD码 */
  kdCode: string
  /** 二维码数据 */
  qrCodeData: string
  /** 产品列表 */
  productList: ProductInfo[]
  /** 装箱时间 */
  packingTime: string
  /** 操作员 */
  operator?: string
}

// 打印信息接口
export interface PrintInfo {
  /** 项目代码 */
  projectCode: string
  /** 批次码 */
  batchCode: string
  /** KD码 */
  kdCode: string
  /** 二维码数据 */
  qrCodeData?: string
  /** 装箱时间 */
  packingTime?: string
}

// API响应基础接口
export interface ApiResponse<T = any> {
  /** 响应码 */
  code: number
  /** 响应消息 */
  message: string
  /** 响应数据 */
  data: T
  /** 是否成功 */
  success: boolean
}

// 工单列表响应接口
export interface WorkOrderListResponse {
  /** 工单列表 */
  workOrders: WorkOrderOption[]
  /** 总数 */
  total: number
}

// 工单详情响应接口
export interface WorkOrderDetailResponse {
  /** 工单信息 */
  workOrderInfo: WorkOrderInfo
}
